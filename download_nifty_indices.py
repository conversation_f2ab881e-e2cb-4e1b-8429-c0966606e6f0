#!/usr/bin/env python3
"""
<PERSON>ript to download historical data for Nifty indices from Yahoo Finance
Downloads maximum available historical data till today in CSV format
"""

import yfinance as yf
import pandas as pd
import os
from datetime import datetime, timedelta
import time

# Dictionary mapping index names to their Yahoo Finance symbols
# Using NSE symbols that are more likely to work with yfinance
NIFTY_INDICES = {
    "Nifty Auto Index": "^CNXAUTO",
    "Nifty Bank Index": "^NSEBANK",
    "Nifty Chemicals": "^CNXCHEM",
    "Nifty Financial Services Index": "NIFTYFINSRV.NS",
    "Nifty Financial Services 25/50 Index": "NIFTYFINANCE.NS",
    "Nifty Financial Services Ex-Bank index": "NIFTYFINEXBANK.NS",
    "Nifty FMCG Index": "^CNXFMCG",
    "Nifty Healthcare Index": "NIFTYHEALTHCARE.NS",
    "Nifty IT Index": "^CNXIT",
    "Nifty Media Index": "^CNXMEDIA",
    "Nifty Metal Index": "^CNXMETAL",
    "Nifty Pharma Index": "^CNXPHARMA",
    "Nifty Private Bank Index": "NIFTYPVTBANK.NS",
    "Nifty PSU Bank Index": "NIFTYPSUBANK.NS",
    "Nifty Realty Index": "^CNXREALTY",
    "Nifty Consumer Durables Index": "NIFTYCONSDUR.NS",
    "Nifty Oil and Gas Index": "NIFTYOILGAS.NS",
    "Nifty500 Healthcare": "NIFTY500HEALTH.NS",
    "Nifty MidSmall Financial Services Index": "NIFTYMIDSMALLFS.NS",
    "Nifty MidSmall Healthcare Index": "NIFTYMIDSMALLHEALTH.NS",
    "Nifty MidSmall IT & Telecom Index": "NIFTYMIDSMALLIT.NS"
}

def download_index_data(symbol, index_name):
    """
    Download historical data for a given index symbol

    Args:
        symbol (str): Yahoo Finance symbol for the index
        index_name (str): Human readable name of the index

    Returns:
        pandas.DataFrame: Historical data or None if failed
    """
    try:
        print(f"Downloading data for {index_name} ({symbol})...")

        # Create ticker object
        ticker = yf.Ticker(symbol)

        # Download historical data with maximum period
        # Using period="max" to get maximum available historical data
        hist_data = ticker.history(period="max", interval="1d")

        if hist_data.empty:
            print(f"  ❌ No data available for {index_name}")
            return None
        
        # Reset index to make Date a column
        hist_data.reset_index(inplace=True)
        
        # Add Adj Close column (same as Close for indices)
        if 'Adj Close' not in hist_data.columns:
            hist_data['Adj Close'] = hist_data['Close']

        # Reorder columns to match the required format
        final_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Adj Close', 'Volume']

        # Check if all required columns exist
        missing_cols = [col for col in final_columns if col not in hist_data.columns]
        if missing_cols:
            print(f"  ⚠️  Missing columns: {missing_cols}")
            # Fill missing Volume with 0 if it's the only missing column
            if missing_cols == ['Volume']:
                hist_data['Volume'] = 0
            else:
                print(f"  ❌ Cannot proceed due to missing essential columns")
                return None

        hist_data = hist_data[final_columns]
        
        # Format date column
        hist_data['Date'] = hist_data['Date'].dt.strftime('%Y-%m-%d')
        
        print(f"  ✅ Downloaded {len(hist_data)} records from {hist_data['Date'].iloc[0]} to {hist_data['Date'].iloc[-1]}")
        
        return hist_data
        
    except Exception as e:
        print(f"  ❌ Error downloading {index_name}: {str(e)}")
        return None

def main():
    """Main function to download all indices data"""
    
    print("🚀 Starting download of Nifty indices historical data...")
    print(f"📅 Current date: {datetime.now().strftime('%Y-%m-%d')}")
    print(f"📊 Total indices to download: {len(NIFTY_INDICES)}")
    print("-" * 60)
    
    # Create output directory
    output_dir = "nifty_indices_data"
    os.makedirs(output_dir, exist_ok=True)
    
    successful_downloads = 0
    failed_downloads = 0
    
    for index_name, symbol in NIFTY_INDICES.items():
        # Download data
        data = download_index_data(symbol, index_name)
        
        if data is not None:
            # Create safe filename
            safe_filename = index_name.replace(" ", "_").replace("/", "_").replace("&", "and")
            csv_filename = f"{safe_filename}.csv"
            csv_path = os.path.join(output_dir, csv_filename)
            
            # Save to CSV
            data.to_csv(csv_path, index=False)
            print(f"  💾 Saved to: {csv_path}")
            successful_downloads += 1
        else:
            failed_downloads += 1
        
        # Add small delay to avoid rate limiting
        time.sleep(0.5)
        print()
    
    print("-" * 60)
    print(f"📈 Download Summary:")
    print(f"  ✅ Successful: {successful_downloads}")
    print(f"  ❌ Failed: {failed_downloads}")
    print(f"  📁 Data saved in: {output_dir}/")
    
    if successful_downloads > 0:
        print(f"\n🎉 Successfully downloaded historical data for {successful_downloads} indices!")
        print(f"📋 Each CSV file contains columns: Date, Open, High, Low, Close, Adj Close, Volume")

if __name__ == "__main__":
    main()
