#!/usr/bin/env python3
"""
Final comprehensive script to download historical data for Nifty indices
Includes all requested indices plus some additional popular ones
Downloads maximum available historical data till today in CSV format
"""

import yfinance as yf
import pandas as pd
import os
from datetime import datetime
import time

# Comprehensive dictionary of Nifty indices with multiple symbol attempts
NIFTY_INDICES = {
    # Successfully working indices
    "Nifty Auto Index": ["^CNXAUTO"],
    "Nifty Bank Index": ["^NSEBANK"], 
    "Nifty FMCG Index": ["^CNXFMCG"],
    "Nifty IT Index": ["^CNXIT"],
    "Nifty Media Index": ["^CNXMEDIA"],
    "Nifty Metal Index": ["^CNXMETAL"],
    "Nifty Pharma Index": ["^CNXPHARMA"],
    "Nifty PSU Bank Index": ["^CNXPSUBANK"],
    "Nifty Realty Index": ["^CNXREALTY"],
    
    # Additional popular indices that might work
    "Nifty 50": ["^NSEI"],
    "Nifty Next 50": ["^NSMIDCP"],
    "Nifty 100": ["^CNX100"],
    "Nifty 200": ["^CNX200"],
    "Nifty 500": ["^CNX500"],
    "Nifty Midcap 50": ["^NSEMDCP50"],
    "Nifty Midcap 100": ["^CNXMIDCAP"],
    "Nifty Smallcap 100": ["^CNXSC"],
    "Nifty Energy": ["^CNXENERGY"],
    "Nifty Infrastructure": ["^CNXINFRA"],
    "Nifty Services Sector": ["^CNXSERVICE"],
    "Nifty Commodities": ["^CNXCOMMODITY"],
    "Nifty Consumption": ["^CNXCONSUMPTION"],
    "Nifty CPSE": ["^CNXCPSE"],
    "Nifty Dividend Opportunities 50": ["^CNXDIVIDEND"],
    "Nifty Growth Sectors 15": ["^CNXGROWTH"],
    "Nifty High Beta 50": ["^CNXHIGHBETA"],
    "Nifty Low Volatility 50": ["^CNXLOWVOL"],
    "Nifty Quality 30": ["^CNXQUALITY"],
    "Nifty Value 20": ["^CNXVALUE"],
    
    # Problematic indices - trying alternative symbols
    "Nifty Chemicals": ["^CNXCHEM", "NIFTYCHEM.NS", "NIFTYCHEMICALS.NS"],
    "Nifty Financial Services Index": ["^CNXFIN", "NIFTYFINSRV.NS", "NIFTYFINSERVICE.NS", "NIFTYFINANCIALSERVICES.NS"],
    "Nifty Financial Services 25/50 Index": ["NIFTYFINANCE.NS", "^CNXFINANCE", "NIFTYFINANCIALSERVICES2550.NS"],
    "Nifty Financial Services Ex-Bank index": ["NIFTYFINEXBANK.NS", "^CNXFINEXBANK", "NIFTYFINANCIALSERVICESEXBANK.NS"],
    "Nifty Healthcare Index": ["NIFTYHEALTHCARE.NS", "^CNXHEALTH", "NIFTYHEALTH.NS"],
    "Nifty Private Bank Index": ["NIFTYPVTBANK.NS", "^CNXPVTBANK", "NIFTYPRIVATEBANK.NS"],
    "Nifty Consumer Durables Index": ["NIFTYCONSDUR.NS", "^CNXCONSDUR", "NIFTYCONSUMERDURABLES.NS"],
    "Nifty Oil and Gas Index": ["NIFTYOILGAS.NS", "^CNXOILGAS", "NIFTYOILANDGAS.NS"],
    "Nifty500 Healthcare": ["NIFTY500HEALTH.NS", "^CNX500HEALTH", "NIFTY500HEALTHCARE.NS"],
    "Nifty MidSmall Financial Services Index": ["NIFTYMIDSMALLFS.NS", "^CNXMIDSMALLFS"],
    "Nifty MidSmall Healthcare Index": ["NIFTYMIDSMALLHEALTH.NS", "^CNXMIDSMALLHEALTH"],
    "Nifty MidSmall IT & Telecom Index": ["NIFTYMIDSMALLIT.NS", "^CNXMIDSMALLIT"]
}

def download_index_data(symbols, index_name):
    """
    Download historical data for a given index, trying multiple symbols
    """
    for symbol in symbols:
        try:
            print(f"  Trying symbol: {symbol}")
            
            ticker = yf.Ticker(symbol)
            
            # Try different period approaches
            hist_data = None
            try:
                hist_data = ticker.history(period="max", interval="1d")
            except:
                try:
                    # Fallback to specific date range
                    hist_data = ticker.history(start="2000-01-01", end=datetime.now().strftime('%Y-%m-%d'), interval="1d")
                except:
                    continue
            
            if hist_data is None or hist_data.empty:
                continue
            
            # Reset index to make Date a column
            hist_data.reset_index(inplace=True)
            
            # Add Adj Close column if missing
            if 'Adj Close' not in hist_data.columns:
                hist_data['Adj Close'] = hist_data['Close']
            
            # Handle missing Volume column
            if 'Volume' not in hist_data.columns:
                hist_data['Volume'] = 0
            
            # Reorder columns
            final_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Adj Close', 'Volume']
            
            # Check for essential columns
            essential_cols = ['Date', 'Open', 'High', 'Low', 'Close']
            missing_essential = [col for col in essential_cols if col not in hist_data.columns]
            if missing_essential:
                continue
            
            hist_data = hist_data[final_columns]
            
            # Format date column
            hist_data['Date'] = hist_data['Date'].dt.strftime('%Y-%m-%d')
            
            print(f"  ✅ Success! {len(hist_data)} records from {hist_data['Date'].iloc[0]} to {hist_data['Date'].iloc[-1]}")
            return hist_data, symbol
            
        except Exception as e:
            print(f"  ⚠️  Error with {symbol}: {str(e)[:50]}...")
            continue
    
    return None, None

def main():
    """Main function to download all indices data"""
    
    print("🚀 Final comprehensive download of Nifty indices historical data...")
    print(f"📅 Current date: {datetime.now().strftime('%Y-%m-%d')}")
    print(f"📊 Total indices to download: {len(NIFTY_INDICES)}")
    print("=" * 80)
    
    # Create output directory
    output_dir = "nifty_indices_final"
    os.makedirs(output_dir, exist_ok=True)
    
    successful_downloads = 0
    failed_downloads = 0
    download_summary = []
    
    for index_name, symbols in NIFTY_INDICES.items():
        print(f"\n📈 {index_name}")
        print("-" * 60)
        
        # Download data
        data, successful_symbol = download_index_data(symbols, index_name)
        
        if data is not None:
            # Create safe filename
            safe_filename = index_name.replace(" ", "_").replace("/", "_").replace("&", "and")
            csv_filename = f"{safe_filename}.csv"
            csv_path = os.path.join(output_dir, csv_filename)
            
            # Save to CSV
            data.to_csv(csv_path, index=False)
            print(f"  💾 Saved to: {csv_filename}")
            successful_downloads += 1
            
            download_summary.append({
                'Index': index_name,
                'Symbol': successful_symbol,
                'Records': len(data),
                'From': data['Date'].iloc[0],
                'To': data['Date'].iloc[-1],
                'Status': 'Success'
            })
        else:
            print(f"  ❌ Failed to download data")
            failed_downloads += 1
            download_summary.append({
                'Index': index_name,
                'Symbol': 'N/A',
                'Records': 0,
                'From': 'N/A',
                'To': 'N/A',
                'Status': 'Failed'
            })
        
        # Small delay to avoid rate limiting
        time.sleep(0.3)
    
    # Save summary report
    summary_df = pd.DataFrame(download_summary)
    summary_path = os.path.join(output_dir, "download_summary.csv")
    summary_df.to_csv(summary_path, index=False)
    
    print("\n" + "=" * 80)
    print(f"📈 FINAL DOWNLOAD SUMMARY:")
    print(f"  ✅ Successful: {successful_downloads}")
    print(f"  ❌ Failed: {failed_downloads}")
    print(f"  📁 Data saved in: {output_dir}/")
    print(f"  📋 Summary report: download_summary.csv")
    
    if successful_downloads > 0:
        print(f"\n🎉 Successfully downloaded historical data for {successful_downloads} indices!")
        print(f"📋 Each CSV file contains columns: Date, Open, High, Low, Close, Adj Close, Volume")
        print(f"📊 Data ranges from as early as 2007 to {datetime.now().strftime('%Y-%m-%d')}")

if __name__ == "__main__":
    main()
