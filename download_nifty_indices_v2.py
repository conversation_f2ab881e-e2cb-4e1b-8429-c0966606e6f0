#!/usr/bin/env python3
"""
Enhanced script to download historical data for Nifty indices from Yahoo Finance
Downloads maximum available historical data till today in CSV format
Uses multiple symbol formats to maximize success rate
"""

import yfinance as yf
import pandas as pd
import os
from datetime import datetime
import time

# Dictionary mapping index names to their possible Yahoo Finance symbols
# Multiple symbols per index to try different formats
NIFTY_INDICES = {
    "Nifty Auto Index": ["^CNXAUTO", "NIFTYAUTO.NS"],
    "Nifty Bank Index": ["^NSEBANK", "NIFTYBANK.NS"], 
    "Nifty Chemicals": ["^CNXCHEM", "NIFTYCHEM.NS"],
    "Nifty Financial Services Index": ["^CNXFIN", "NIFTYFINSRV.NS", "NIFTYFINSERVICE.NS"],
    "Nifty Financial Services 25/50 Index": ["NIFTYFINANCE.NS", "^CNXFINANCE"],
    "Nifty Financial Services Ex-Bank index": ["NIFTYFINEXBANK.NS", "^CNXFINEXBANK"],
    "Nifty FMCG Index": ["^CNXFMCG", "NIFTYFMCG.NS"],
    "Nifty Healthcare Index": ["NIFTYHEALTHCARE.NS", "^CNXHEALTH"],
    "Nifty IT Index": ["^CNXIT", "NIFTYIT.NS"],
    "Nifty Media Index": ["^CNXMEDIA", "NIFTYMEDIA.NS"],
    "Nifty Metal Index": ["^CNXMETAL", "NIFTYMETAL.NS"],
    "Nifty Pharma Index": ["^CNXPHARMA", "NIFTYPHARMA.NS"],
    "Nifty Private Bank Index": ["NIFTYPVTBANK.NS", "^CNXPVTBANK"],
    "Nifty PSU Bank Index": ["NIFTYPSUBANK.NS", "^CNXPSUBANK"],
    "Nifty Realty Index": ["^CNXREALTY", "NIFTYREALTY.NS"],
    "Nifty Consumer Durables Index": ["NIFTYCONSDUR.NS", "^CNXCONSDUR"],
    "Nifty Oil and Gas Index": ["NIFTYOILGAS.NS", "^CNXOILGAS"],
    "Nifty500 Healthcare": ["NIFTY500HEALTH.NS", "^CNX500HEALTH"],
    "Nifty MidSmall Financial Services Index": ["NIFTYMIDSMALLFS.NS", "^CNXMIDSMALLFS"],
    "Nifty MidSmall Healthcare Index": ["NIFTYMIDSMALLHEALTH.NS", "^CNXMIDSMALLHEALTH"],
    "Nifty MidSmall IT & Telecom Index": ["NIFTYMIDSMALLIT.NS", "^CNXMIDSMALLIT"]
}

def download_index_data(symbols, index_name):
    """
    Download historical data for a given index, trying multiple symbols
    
    Args:
        symbols (list): List of possible Yahoo Finance symbols for the index
        index_name (str): Human readable name of the index
    
    Returns:
        pandas.DataFrame: Historical data or None if failed
    """
    for symbol in symbols:
        try:
            print(f"Trying {index_name} with symbol ({symbol})...")
            
            # Create ticker object
            ticker = yf.Ticker(symbol)
            
            # Download historical data with maximum period
            hist_data = ticker.history(period="max", interval="1d")
            
            if hist_data.empty:
                print(f"  ⚠️  No data for symbol {symbol}")
                continue
            
            # Reset index to make Date a column
            hist_data.reset_index(inplace=True)
            
            # Add Adj Close column (same as Close for indices)
            if 'Adj Close' not in hist_data.columns:
                hist_data['Adj Close'] = hist_data['Close']
            
            # Reorder columns to match the required format
            final_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Adj Close', 'Volume']
            
            # Check if all required columns exist
            missing_cols = [col for col in final_columns if col not in hist_data.columns]
            if missing_cols:
                print(f"  ⚠️  Missing columns: {missing_cols}")
                # Fill missing Volume with 0 if it's the only missing column
                if missing_cols == ['Volume']:
                    hist_data['Volume'] = 0
                else:
                    print(f"  ❌ Cannot proceed due to missing essential columns")
                    continue
            
            hist_data = hist_data[final_columns]
            
            # Format date column
            hist_data['Date'] = hist_data['Date'].dt.strftime('%Y-%m-%d')
            
            print(f"  ✅ Downloaded {len(hist_data)} records from {hist_data['Date'].iloc[0]} to {hist_data['Date'].iloc[-1]}")
            
            return hist_data, symbol
            
        except Exception as e:
            print(f"  ⚠️  Error with symbol {symbol}: {str(e)}")
            continue
    
    print(f"  ❌ Failed to download data for {index_name} with any symbol")
    return None, None

def main():
    """Main function to download all indices data"""
    
    print("🚀 Starting enhanced download of Nifty indices historical data...")
    print(f"📅 Current date: {datetime.now().strftime('%Y-%m-%d')}")
    print(f"📊 Total indices to download: {len(NIFTY_INDICES)}")
    print("-" * 60)
    
    # Create output directory
    output_dir = "nifty_indices_data_v2"
    os.makedirs(output_dir, exist_ok=True)
    
    successful_downloads = 0
    failed_downloads = 0
    download_summary = []
    
    for index_name, symbols in NIFTY_INDICES.items():
        # Download data
        data, successful_symbol = download_index_data(symbols, index_name)
        
        if data is not None:
            # Create safe filename
            safe_filename = index_name.replace(" ", "_").replace("/", "_").replace("&", "and")
            csv_filename = f"{safe_filename}.csv"
            csv_path = os.path.join(output_dir, csv_filename)
            
            # Save to CSV
            data.to_csv(csv_path, index=False)
            print(f"  💾 Saved to: {csv_path}")
            successful_downloads += 1
            
            download_summary.append({
                'Index': index_name,
                'Symbol': successful_symbol,
                'Records': len(data),
                'From': data['Date'].iloc[0],
                'To': data['Date'].iloc[-1],
                'Status': 'Success'
            })
        else:
            failed_downloads += 1
            download_summary.append({
                'Index': index_name,
                'Symbol': 'N/A',
                'Records': 0,
                'From': 'N/A',
                'To': 'N/A',
                'Status': 'Failed'
            })
        
        # Add small delay to avoid rate limiting
        time.sleep(0.5)
        print()
    
    # Save summary report
    summary_df = pd.DataFrame(download_summary)
    summary_path = os.path.join(output_dir, "download_summary.csv")
    summary_df.to_csv(summary_path, index=False)
    
    print("-" * 60)
    print(f"📈 Download Summary:")
    print(f"  ✅ Successful: {successful_downloads}")
    print(f"  ❌ Failed: {failed_downloads}")
    print(f"  📁 Data saved in: {output_dir}/")
    print(f"  📋 Summary report: {summary_path}")
    
    if successful_downloads > 0:
        print(f"\n🎉 Successfully downloaded historical data for {successful_downloads} indices!")
        print(f"📋 Each CSV file contains columns: Date, Open, High, Low, Close, Adj Close, Volume")

if __name__ == "__main__":
    main()
